"""
Classe base para todos os agentes do sistema
"""
import ollama
from typing import Dict, Any, List, Optional
import logging
from abc import ABC, abstractmethod
import json
from datetime import datetime

from config.settings import OLLAMA_CONFIG, AGENTS_CONFIG

class BaseAgent(ABC):
    """Classe base abstrata para todos os agentes"""
    
    def __init__(self, agent_type: str, model: str = None):
        self.agent_type = agent_type
        self.model = model or OLLAMA_CONFIG["model"]
        self.config = AGENTS_CONFIG.get(agent_type, {})
        self.logger = logging.getLogger(f"{__name__}.{agent_type}")
        
        # Configurações do agente
        self.name = self.config.get("name", f"Agente {agent_type}")
        self.role = self.config.get("role", "Agente especializado")
        self.max_tokens = self.config.get("max_tokens", 2048)
        self.temperature = self.config.get("temperature", 0.2)
        
        # Histórico de conversas
        self.conversation_history = []
        
        # Verificar conexão com Ollama
        self._check_ollama_connection()
    
    def _check_ollama_connection(self):
        """Verifica se o Ollama está disponível"""
        try:
            ollama.list()
            self.logger.info(f"Conexão com Ollama estabelecida para {self.name}")
        except Exception as e:
            self.logger.error(f"Erro ao conectar com Ollama: {e}")
            raise ConnectionError("Ollama não está disponível")
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """Retorna o prompt do sistema específico do agente"""
        pass
    
    def _format_message(self, role: str, content: str) -> Dict[str, str]:
        """Formata mensagem para o modelo"""
        return {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
    
    def add_to_history(self, role: str, content: str):
        """Adiciona mensagem ao histórico"""
        message = self._format_message(role, content)
        self.conversation_history.append(message)
        
        # Limitar histórico para evitar overflow
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
    
    def generate_response(self, user_input: str, context: str = None) -> str:
        """Gera resposta usando o modelo Ollama"""
        try:
            # Preparar mensagens
            messages = [
                {"role": "system", "content": self.get_system_prompt()}
            ]
            
            # Adicionar contexto se fornecido
            if context:
                messages.append({
                    "role": "system", 
                    "content": f"Contexto adicional: {context}"
                })
            
            # Adicionar histórico recente
            for msg in self.conversation_history[-5:]:  # Últimas 5 mensagens
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            # Adicionar entrada do usuário
            messages.append({"role": "user", "content": user_input})
            
            # Gerar resposta
            response = ollama.chat(
                model=self.model,
                messages=messages,
                options={
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens
                }
            )
            
            assistant_response = response["message"]["content"]
            
            # Adicionar ao histórico
            self.add_to_history("user", user_input)
            self.add_to_history("assistant", assistant_response)
            
            return assistant_response
            
        except Exception as e:
            self.logger.error(f"Erro ao gerar resposta: {e}")
            return f"Desculpe, ocorreu um erro ao processar sua solicitação: {str(e)}"
    
    def clear_history(self):
        """Limpa o histórico de conversas"""
        self.conversation_history = []
        self.logger.info(f"Histórico limpo para {self.name}")
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Retorna informações sobre o agente"""
        return {
            "name": self.name,
            "role": self.role,
            "type": self.agent_type,
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "conversation_length": len(self.conversation_history)
        }
    
    def export_conversation(self) -> List[Dict[str, Any]]:
        """Exporta o histórico de conversas"""
        return self.conversation_history.copy()
    
    def import_conversation(self, conversation: List[Dict[str, Any]]):
        """Importa histórico de conversas"""
        self.conversation_history = conversation
        self.logger.info(f"Histórico importado para {self.name}")

class AgentResponse:
    """Classe para estruturar respostas dos agentes"""
    
    def __init__(self, content: str, agent_name: str, confidence: float = 1.0, 
                 metadata: Dict[str, Any] = None):
        self.content = content
        self.agent_name = agent_name
        self.confidence = confidence
        self.metadata = metadata or {}
        self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte resposta para dicionário"""
        return {
            "content": self.content,
            "agent_name": self.agent_name,
            "confidence": self.confidence,
            "metadata": self.metadata,
            "timestamp": self.timestamp
        }
    
    def __str__(self) -> str:
        return f"[{self.agent_name}] {self.content}"
