"""
Agente especializado em síntese e geração de relatórios médicos
"""
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
import json

from agents.base_agent import BaseAgent, AgentResponse
from config.settings import SYSTEM_PROMPTS

class SynthesisAgent(BaseAgent):
    """Agente responsável por síntese de informações e geração de relatórios"""
    
    def __init__(self, model: str = None):
        super().__init__("synthesis_agent", model)
        self.logger = logging.getLogger(__name__)
    
    def get_system_prompt(self) -> str:
        """Retorna o prompt do sistema para síntese"""
        return SYSTEM_PROMPTS["synthesis_agent"]
    
    def synthesize_multi_agent_responses(self, agent_responses: List[AgentResponse]) -> AgentResponse:
        """Sintetiza respostas de múltiplos agentes em uma resposta unificada"""
        try:
            if not agent_responses:
                return AgentResponse(
                    content="Nenhuma resposta de agente fornecida para síntese.",
                    agent_name=self.name,
                    confidence=0.0
                )
            
            # Preparar conteúdo para síntese
            synthesis_content = self._prepare_synthesis_content(agent_responses)
            
            synthesis_prompt = f"""
            Sintetize as seguintes respostas de agentes especializados em neurocovid em uma resposta unificada e coerente:
            
            {synthesis_content}
            
            A síntese deve:
            1. Integrar as informações de forma lógica
            2. Resolver contradições, se houver
            3. Destacar consensos e divergências
            4. Fornecer uma conclusão clara e acionável
            5. Manter rigor científico e médico
            """
            
            synthesized_response = self.generate_response(synthesis_prompt)
            
            # Calcular confiança média ponderada
            avg_confidence = self._calculate_weighted_confidence(agent_responses)
            
            return AgentResponse(
                content=synthesized_response,
                agent_name=self.name,
                confidence=avg_confidence,
                metadata={
                    "agents_synthesized": [resp.agent_name for resp in agent_responses],
                    "synthesis_type": "multi_agent",
                    "original_responses_count": len(agent_responses)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro na síntese de respostas: {e}")
            return AgentResponse(
                content=f"Erro ao sintetizar respostas: {str(e)}",
                agent_name=self.name,
                confidence=0.0
            )
    
    def generate_clinical_report(self, patient_data: Dict[str, Any], 
                               analysis_results: List[AgentResponse]) -> AgentResponse:
        """Gera relatório clínico estruturado"""
        try:
            # Preparar dados do paciente
            patient_summary = self._format_patient_data(patient_data)
            
            # Preparar resultados de análise
            analysis_summary = self._format_analysis_results(analysis_results)
            
            report_prompt = f"""
            Gere um relatório clínico estruturado sobre manifestações neurológicas da COVID-19:
            
            DADOS DO PACIENTE:
            {patient_summary}
            
            RESULTADOS DA ANÁLISE:
            {analysis_summary}
            
            O relatório deve seguir a estrutura:
            1. RESUMO EXECUTIVO
            2. APRESENTAÇÃO CLÍNICA
            3. ANÁLISE DOS SINTOMAS NEUROLÓGICOS
            4. CORRELAÇÃO COM LITERATURA MÉDICA
            5. RECOMENDAÇÕES TERAPÊUTICAS
            6. PROGNÓSTICO
            7. SEGUIMENTO RECOMENDADO
            
            Use linguagem médica apropriada e baseie-se em evidências científicas.
            """
            
            clinical_report = self.generate_response(report_prompt)
            
            return AgentResponse(
                content=clinical_report,
                agent_name=self.name,
                confidence=0.9,
                metadata={
                    "report_type": "clinical",
                    "patient_id": patient_data.get("id", "N/A"),
                    "analysis_sources": len(analysis_results),
                    "generated_at": datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro ao gerar relatório clínico: {e}")
            return AgentResponse(
                content=f"Erro ao gerar relatório: {str(e)}",
                agent_name=self.name,
                confidence=0.0
            )
    
    def generate_research_summary(self, documents: List[Dict[str, Any]], 
                                 research_question: str) -> AgentResponse:
        """Gera resumo de pesquisa baseado em documentos científicos"""
        try:
            # Preparar conteúdo dos documentos
            documents_content = self._format_research_documents(documents)
            
            research_prompt = f"""
            Com base nos seguintes artigos científicos sobre neurocovid, gere um resumo de pesquisa 
            que responda à questão: "{research_question}"
            
            DOCUMENTOS ANALISADOS:
            {documents_content}
            
            O resumo deve incluir:
            1. INTRODUÇÃO E CONTEXTO
            2. METODOLOGIA DOS ESTUDOS
            3. PRINCIPAIS ACHADOS
            4. ANÁLISE CRÍTICA DOS RESULTADOS
            5. LIMITAÇÕES DOS ESTUDOS
            6. CONCLUSÕES E IMPLICAÇÕES CLÍNICAS
            7. DIREÇÕES PARA PESQUISAS FUTURAS
            
            Mantenha rigor científico e cite evidências específicas.
            """
            
            research_summary = self.generate_response(research_prompt)
            
            return AgentResponse(
                content=research_summary,
                agent_name=self.name,
                confidence=0.85,
                metadata={
                    "summary_type": "research",
                    "research_question": research_question,
                    "documents_analyzed": len(documents),
                    "generated_at": datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro ao gerar resumo de pesquisa: {e}")
            return AgentResponse(
                content=f"Erro ao gerar resumo: {str(e)}",
                agent_name=self.name,
                confidence=0.0
            )
    
    def create_comparative_analysis(self, topic: str, 
                                  agent_responses: List[AgentResponse]) -> AgentResponse:
        """Cria análise comparativa de diferentes perspectivas"""
        try:
            # Agrupar respostas por agente
            responses_by_agent = {}
            for response in agent_responses:
                agent_name = response.agent_name
                if agent_name not in responses_by_agent:
                    responses_by_agent[agent_name] = []
                responses_by_agent[agent_name].append(response)
            
            # Preparar análise comparativa
            comparison_content = self._format_comparative_content(responses_by_agent, topic)
            
            comparison_prompt = f"""
            Realize uma análise comparativa sobre "{topic}" baseada nas diferentes perspectivas dos agentes especializados:
            
            {comparison_content}
            
            A análise deve:
            1. Identificar pontos de convergência entre as perspectivas
            2. Destacar divergências e suas possíveis causas
            3. Avaliar a qualidade e confiabilidade de cada perspectiva
            4. Sintetizar uma visão integrada do tópico
            5. Fornecer recomendações baseadas na análise comparativa
            """
            
            comparative_analysis = self.generate_response(comparison_prompt)
            
            return AgentResponse(
                content=comparative_analysis,
                agent_name=self.name,
                confidence=0.8,
                metadata={
                    "analysis_type": "comparative",
                    "topic": topic,
                    "agents_compared": list(responses_by_agent.keys()),
                    "total_responses": len(agent_responses)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro na análise comparativa: {e}")
            return AgentResponse(
                content=f"Erro ao criar análise comparativa: {str(e)}",
                agent_name=self.name,
                confidence=0.0
            )
    
    def _prepare_synthesis_content(self, responses: List[AgentResponse]) -> str:
        """Prepara conteúdo para síntese"""
        content_parts = []
        
        for i, response in enumerate(responses):
            content_parts.append(
                f"AGENTE {i+1} - {response.agent_name} (Confiança: {response.confidence}):\n"
                f"{response.content}\n"
            )
        
        return "\n".join(content_parts)
    
    def _calculate_weighted_confidence(self, responses: List[AgentResponse]) -> float:
        """Calcula confiança média ponderada"""
        if not responses:
            return 0.0
        
        total_confidence = sum(resp.confidence for resp in responses)
        return round(total_confidence / len(responses), 2)
    
    def _format_patient_data(self, patient_data: Dict[str, Any]) -> str:
        """Formata dados do paciente para relatório"""
        formatted = []
        for key, value in patient_data.items():
            formatted.append(f"{key.replace('_', ' ').title()}: {value}")
        return "\n".join(formatted)
    
    def _format_analysis_results(self, results: List[AgentResponse]) -> str:
        """Formata resultados de análise"""
        formatted = []
        for result in results:
            formatted.append(
                f"Análise por {result.agent_name}:\n{result.content[:500]}...\n"
            )
        return "\n".join(formatted)
    
    def _format_research_documents(self, documents: List[Dict[str, Any]]) -> str:
        """Formata documentos de pesquisa"""
        formatted = []
        for i, doc in enumerate(documents):
            formatted.append(
                f"Documento {i+1}: {doc.get('title', 'Título não disponível')}\n"
                f"Conteúdo: {doc.get('content', '')[:800]}...\n"
            )
        return "\n".join(formatted)
    
    def _format_comparative_content(self, responses_by_agent: Dict[str, List[AgentResponse]], 
                                  topic: str) -> str:
        """Formata conteúdo para análise comparativa"""
        formatted = []
        
        for agent_name, responses in responses_by_agent.items():
            formatted.append(f"PERSPECTIVA DO {agent_name.upper()}:")
            for response in responses:
                formatted.append(f"- {response.content[:300]}...")
            formatted.append("")
        
        return "\n".join(formatted)
