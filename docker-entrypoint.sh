#!/bin/bash

# Script de inicialização para Docker

echo "🧠 Iniciando Sistema de Multiagentes para Neurocovid"
echo "=================================================="

# Iniciar Ollama em background
echo "🚀 Iniciando Ollama..."
ollama serve &
OLLAMA_PID=$!

# Aguardar Ollama estar pronto
echo "⏳ Aguardando Ollama estar pronto..."
sleep 10

# Verificar se Ollama está rodando
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "❌ Erro: Ollama não está respondendo"
    exit 1
fi

echo "✅ Ollama está rodando"

# Baixar modelos necessários se não existirem
echo "📥 Verificando modelos..."

if ! ollama list | grep -q "llama2:7b"; then
    echo "📦 Baixando llama2:7b..."
    ollama pull llama2:7b
fi

if ! ollama list | grep -q "nomic-embed-text"; then
    echo "📦 Baixando nomic-embed-text..."
    ollama pull nomic-embed-text
fi

echo "✅ Modelos prontos"

# Iniciar aplicação Streamlit
echo "🌐 Iniciando interface web..."
echo "📱 Acesse: http://localhost:8501"

# Função para cleanup
cleanup() {
    echo "🛑 Encerrando sistema..."
    kill $OLLAMA_PID 2>/dev/null
    exit 0
}

# Capturar sinais para cleanup
trap cleanup SIGTERM SIGINT

# Executar Streamlit
streamlit run app.py --server.address 0.0.0.0 --server.port 8501 &
STREAMLIT_PID=$!

# Aguardar qualquer processo terminar
wait $STREAMLIT_PID $OLLAMA_PID
