"""
Sistema de banco de dados vetorial para armazenamento de documentos
"""
import chromadb
from chromadb.config import Settings
from typing import List, Dict, Any, Optional
import logging
from sentence_transformers import SentenceTransformer
import numpy as np
from pathlib import Path
import json
import uuid

from utils.pdf_processor import DocumentChunk
from config.settings import VECTOR_DB_CONFIG

class VectorStore:
    """Classe para gerenciar o banco de dados vetorial"""
    
    def __init__(self, persist_directory: str = None, collection_name: str = None):
        self.persist_directory = persist_directory or VECTOR_DB_CONFIG["persist_directory"]
        self.collection_name = collection_name or VECTOR_DB_CONFIG["collection_name"]
        self.logger = logging.getLogger(__name__)
        
        # Inicializar ChromaDB
        self.client = chromadb.PersistentClient(
            path=self.persist_directory,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # Inicializar modelo de embeddings
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Obter ou criar coleção
        self.collection = self._get_or_create_collection()
    
    def _get_or_create_collection(self):
        """Obtém ou cria a coleção no ChromaDB"""
        try:
            # Tentar obter coleção existente
            collection = self.client.get_collection(name=self.collection_name)
            self.logger.info(f"Coleção '{self.collection_name}' carregada")
        except Exception:
            # Criar nova coleção
            collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "Artigos sobre neurocovid"}
            )
            self.logger.info(f"Nova coleção '{self.collection_name}' criada")
        
        return collection
    
    def add_documents(self, chunks: List[DocumentChunk]) -> bool:
        """Adiciona documentos ao banco vetorial"""
        try:
            if not chunks:
                self.logger.warning("Nenhum chunk fornecido para adicionar")
                return False
            
            # Preparar dados para inserção
            documents = []
            metadatas = []
            ids = []
            
            for chunk in chunks:
                documents.append(chunk.content)
                metadatas.append({
                    "file_name": chunk.metadata.get("file_name", ""),
                    "page_number": chunk.page_number,
                    "chunk_id": chunk.chunk_id,
                    "file_path": chunk.metadata.get("file_path", ""),
                    **chunk.metadata
                })
                ids.append(chunk.chunk_id)
            
            # Gerar embeddings
            self.logger.info(f"Gerando embeddings para {len(documents)} chunks")
            embeddings = self.embedding_model.encode(documents).tolist()
            
            # Adicionar à coleção
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                embeddings=embeddings,
                ids=ids
            )
            
            self.logger.info(f"Adicionados {len(chunks)} chunks ao banco vetorial")
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao adicionar documentos: {e}")
            return False
    
    def search_similar(self, query: str, n_results: int = 5, 
                      filter_metadata: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Busca documentos similares à consulta"""
        try:
            # Gerar embedding da consulta
            query_embedding = self.embedding_model.encode([query]).tolist()[0]
            
            # Realizar busca
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where=filter_metadata,
                include=["documents", "metadatas", "distances"]
            )
            
            # Formatar resultados
            formatted_results = []
            for i in range(len(results["documents"][0])):
                formatted_results.append({
                    "content": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "similarity_score": 1 - results["distances"][0][i],  # Converter distância para similaridade
                    "chunk_id": results["metadatas"][0][i].get("chunk_id", "")
                })
            
            return formatted_results
            
        except Exception as e:
            self.logger.error(f"Erro na busca: {e}")
            return []
    
    def get_document_by_id(self, chunk_id: str) -> Optional[Dict[str, Any]]:
        """Obtém documento específico por ID"""
        try:
            results = self.collection.get(
                ids=[chunk_id],
                include=["documents", "metadatas"]
            )
            
            if results["documents"]:
                return {
                    "content": results["documents"][0],
                    "metadata": results["metadatas"][0],
                    "chunk_id": chunk_id
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Erro ao obter documento: {e}")
            return None
    
    def delete_document(self, chunk_id: str) -> bool:
        """Remove documento do banco"""
        try:
            self.collection.delete(ids=[chunk_id])
            self.logger.info(f"Documento {chunk_id} removido")
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao remover documento: {e}")
            return False
    
    def list_documents(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Lista documentos no banco"""
        try:
            results = self.collection.get(
                limit=limit,
                include=["documents", "metadatas"]
            )
            
            documents = []
            for i in range(len(results["documents"])):
                documents.append({
                    "content": results["documents"][i][:200] + "...",  # Prévia
                    "metadata": results["metadatas"][i],
                    "chunk_id": results["ids"][i]
                })
            
            return documents
            
        except Exception as e:
            self.logger.error(f"Erro ao listar documentos: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Obtém estatísticas da coleção"""
        try:
            count = self.collection.count()
            
            # Obter arquivos únicos
            results = self.collection.get(include=["metadatas"])
            unique_files = set()
            for metadata in results["metadatas"]:
                unique_files.add(metadata.get("file_name", ""))
            
            return {
                "total_chunks": count,
                "unique_files": len(unique_files),
                "files": list(unique_files)
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao obter estatísticas: {e}")
            return {"total_chunks": 0, "unique_files": 0, "files": []}
    
    def reset_collection(self) -> bool:
        """Reseta a coleção (remove todos os documentos)"""
        try:
            self.client.delete_collection(name=self.collection_name)
            self.collection = self._get_or_create_collection()
            self.logger.info("Coleção resetada")
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao resetar coleção: {e}")
            return False
