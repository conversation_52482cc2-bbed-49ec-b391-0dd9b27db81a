"""
Testes para o sistema de multiagentes de neurocovid
"""
import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Adicionar diretório raiz ao path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.vector_store import VectorStore
from utils.pdf_processor import PDFProcessor, DocumentChunk
from agents.base_agent import BaseAgent, AgentResponse
from agents.search_agent import SearchAgent
from agents.analysis_agent import AnalysisAgent
from agents.synthesis_agent import SynthesisAgent
from agents.coordinator_agent import CoordinatorAgent

class TestPDFProcessor(unittest.TestCase):
    """Testes para o processador de PDF"""
    
    def setUp(self):
        self.processor = PDFProcessor(chunk_size=100, chunk_overlap=20)
    
    def test_create_chunks(self):
        """Testa criação de chunks"""
        pages = [
            {
                "text": "Este é um texto de teste para verificar a criação de chunks. " * 10,
                "metadata": {"page_number": 1, "file_name": "test.pdf"}
            }
        ]
        
        chunks = self.processor.create_chunks(pages)
        
        self.assertGreater(len(chunks), 0)
        self.assertIsInstance(chunks[0], DocumentChunk)
        self.assertEqual(chunks[0].page_number, 1)

class TestVectorStore(unittest.TestCase):
    """Testes para o banco vetorial"""
    
    def setUp(self):
        # Usar banco em memória para testes
        self.vector_store = VectorStore(
            persist_directory=":memory:",
            collection_name="test_collection"
        )
    
    @patch('chromadb.PersistentClient')
    def test_initialization(self, mock_client):
        """Testa inicialização do banco vetorial"""
        mock_collection = Mock()
        mock_client.return_value.get_collection.return_value = mock_collection
        
        vs = VectorStore()
        self.assertIsNotNone(vs.client)
        self.assertIsNotNone(vs.collection)

class TestBaseAgent(unittest.TestCase):
    """Testes para a classe base dos agentes"""
    
    def setUp(self):
        # Criar agente mock
        class TestAgent(BaseAgent):
            def get_system_prompt(self):
                return "Test system prompt"
        
        with patch('ollama.list'):
            self.agent = TestAgent("test_agent")
    
    def test_agent_initialization(self):
        """Testa inicialização do agente"""
        self.assertEqual(self.agent.agent_type, "test_agent")
        self.assertIsNotNone(self.agent.name)
        self.assertIsNotNone(self.agent.role)
    
    def test_message_formatting(self):
        """Testa formatação de mensagens"""
        message = self.agent._format_message("user", "test content")
        
        self.assertEqual(message["role"], "user")
        self.assertEqual(message["content"], "test content")
        self.assertIn("timestamp", message)
    
    def test_conversation_history(self):
        """Testa histórico de conversas"""
        self.agent.add_to_history("user", "test message")
        
        self.assertEqual(len(self.agent.conversation_history), 1)
        self.assertEqual(self.agent.conversation_history[0]["role"], "user")

class TestSearchAgent(unittest.TestCase):
    """Testes para o agente de busca"""
    
    def setUp(self):
        self.mock_vector_store = Mock()
        
        with patch('ollama.list'):
            self.search_agent = SearchAgent(self.mock_vector_store)
    
    def test_search_documents(self):
        """Testa busca de documentos"""
        # Mock dos resultados da busca
        mock_results = [
            {
                "content": "Test content about neurocovid",
                "metadata": {"file_name": "test.pdf", "page_number": 1},
                "similarity_score": 0.8,
                "chunk_id": "test_chunk_1"
            }
        ]
        
        self.mock_vector_store.search_similar.return_value = mock_results
        
        with patch.object(self.search_agent, 'generate_response', return_value="Test response"):
            response = self.search_agent.search_documents("test query")
        
        self.assertIsInstance(response, AgentResponse)
        self.assertEqual(response.agent_name, self.search_agent.name)
        self.assertGreater(response.confidence, 0)

class TestAnalysisAgent(unittest.TestCase):
    """Testes para o agente de análise"""
    
    def setUp(self):
        with patch('ollama.list'):
            self.analysis_agent = AnalysisAgent()
    
    def test_extract_medical_terms(self):
        """Testa extração de termos médicos"""
        text = "Paciente apresenta cefaleia e anosmia após COVID-19"
        
        symptoms = self.analysis_agent._extract_medical_terms(text, "symptoms")
        
        self.assertIn("cefaleia", symptoms)
        self.assertIn("anosmia", symptoms)
    
    def test_analyze_symptoms(self):
        """Testa análise de sintomas"""
        text = "Paciente com cefaleia e confusão mental"
        
        with patch.object(self.analysis_agent, 'generate_response', return_value="Analysis result"):
            response = self.analysis_agent.analyze_symptoms(text)
        
        self.assertIsInstance(response, AgentResponse)
        self.assertEqual(response.agent_name, self.analysis_agent.name)

class TestSynthesisAgent(unittest.TestCase):
    """Testes para o agente de síntese"""
    
    def setUp(self):
        with patch('ollama.list'):
            self.synthesis_agent = SynthesisAgent()
    
    def test_synthesize_responses(self):
        """Testa síntese de respostas"""
        responses = [
            AgentResponse("Response 1", "Agent 1", 0.8),
            AgentResponse("Response 2", "Agent 2", 0.7)
        ]
        
        with patch.object(self.synthesis_agent, 'generate_response', return_value="Synthesized result"):
            response = self.synthesis_agent.synthesize_multi_agent_responses(responses)
        
        self.assertIsInstance(response, AgentResponse)
        self.assertEqual(response.agent_name, self.synthesis_agent.name)

class TestCoordinatorAgent(unittest.TestCase):
    """Testes para o agente coordenador"""
    
    def setUp(self):
        self.mock_vector_store = Mock()
        
        with patch('ollama.list'):
            self.coordinator = CoordinatorAgent(self.mock_vector_store)
    
    def test_query_strategy_determination(self):
        """Testa determinação de estratégia de consulta"""
        # Teste consulta de busca
        query_type, strategy = self.coordinator._determine_query_strategy("buscar artigos sobre COVID")
        self.assertEqual(query_type.value, "search")
        
        # Teste consulta de análise
        query_type, strategy = self.coordinator._determine_query_strategy("analisar sintomas neurológicos")
        self.assertEqual(query_type.value, "analysis")
    
    def test_process_user_query(self):
        """Testa processamento de consulta do usuário"""
        # Mock das respostas dos agentes
        mock_response = AgentResponse("Test response", "Test Agent", 0.8)
        
        with patch.object(self.coordinator, '_handle_search_query', return_value=mock_response):
            response = self.coordinator.process_user_query("buscar informações sobre neurocovid")
        
        self.assertIsInstance(response, AgentResponse)
        self.assertEqual(len(self.coordinator.interaction_history), 1)

class TestSystemIntegration(unittest.TestCase):
    """Testes de integração do sistema"""
    
    def setUp(self):
        self.mock_vector_store = Mock()
        
        with patch('ollama.list'):
            self.coordinator = CoordinatorAgent(self.mock_vector_store)
    
    def test_end_to_end_workflow(self):
        """Testa fluxo completo do sistema"""
        # Simular upload e processamento de PDF
        chunks = [
            DocumentChunk(
                content="Test content about neurocovid symptoms",
                metadata={"file_name": "test.pdf", "page_number": 1},
                page_number=1,
                chunk_id="test_chunk_1"
            )
        ]
        
        # Simular adição ao banco vetorial
        self.mock_vector_store.add_documents.return_value = True
        
        # Simular consulta do usuário
        mock_search_results = [
            {
                "content": "Test content",
                "metadata": {"file_name": "test.pdf"},
                "similarity_score": 0.8,
                "chunk_id": "test_chunk_1"
            }
        ]
        
        self.mock_vector_store.search_similar.return_value = mock_search_results
        
        with patch.object(self.coordinator, 'generate_response', return_value="Test response"):
            response = self.coordinator.process_user_query("Quais são os sintomas neurológicos?")
        
        self.assertIsInstance(response, AgentResponse)
        self.assertGreater(response.confidence, 0)

def run_tests():
    """Executa todos os testes"""
    # Descobrir e executar testes
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromModule(sys.modules[__name__])
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    print("🧪 Executando testes do sistema...")
    print("=" * 40)
    
    success = run_tests()
    
    if success:
        print("\n✅ Todos os testes passaram!")
    else:
        print("\n❌ Alguns testes falharam!")
        sys.exit(1)
