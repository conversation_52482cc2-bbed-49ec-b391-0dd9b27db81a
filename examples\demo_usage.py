"""
Demonstração de uso do sistema de multiagentes para análise de neurocovid
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.vector_store import VectorStore
from utils.pdf_processor import PDFProcessor
from agents.coordinator_agent import CoordinatorAgent
from agents.search_agent import SearchAgent
from agents.analysis_agent import AnalysisAgent
from agents.synthesis_agent import SynthesisAgent

def demo_basic_usage():
    """Demonstração básica do sistema"""
    print("🧠 Demo: Sistema de Multiagentes para Neurocovid")
    print("=" * 50)
    
    # Inicializar componentes
    print("📚 Inicializando banco vetorial...")
    vector_store = VectorStore()
    
    print("📄 Inicializando processador de PDF...")
    pdf_processor = PDFProcessor()
    
    print("🤖 Inicializando agentes...")
    coordinator = CoordinatorAgent(vector_store)
    
    # Exemplos de consultas
    queries = [
        "Quais são as principais manifestações neurológicas da COVID-19?",
        "Como tratar anosmia pós-COVID?",
        "Compare os sintomas neurológicos entre COVID-19 leve e grave",
        "Analise os mecanismos de neuroinvasão do SARS-CoV-2",
        "Gere um resumo sobre encefalite relacionada à COVID-19"
    ]
    
    print("\n💬 Testando consultas...")
    for i, query in enumerate(queries, 1):
        print(f"\n--- Consulta {i} ---")
        print(f"Pergunta: {query}")
        
        try:
            response = coordinator.process_user_query(query)
            print(f"Agente: {response.agent_name}")
            print(f"Confiança: {response.confidence:.2f}")
            print(f"Resposta: {response.content[:200]}...")
        except Exception as e:
            print(f"Erro: {e}")

def demo_individual_agents():
    """Demonstração dos agentes individuais"""
    print("\n🔬 Demo: Agentes Individuais")
    print("=" * 30)
    
    vector_store = VectorStore()
    
    # Agente de Busca
    print("\n🔍 Testando Agente de Busca...")
    search_agent = SearchAgent(vector_store)
    
    search_response = search_agent.search_documents("manifestações neurológicas COVID-19")
    print(f"Resultados encontrados: {search_response.metadata.get('results_count', 0)}")
    print(f"Confiança: {search_response.confidence:.2f}")
    
    # Agente de Análise
    print("\n🧪 Testando Agente de Análise...")
    analysis_agent = AnalysisAgent()
    
    sample_text = """
    Paciente apresenta cefaleia intensa, anosmia e ageusia há 10 dias após diagnóstico de COVID-19.
    Exame neurológico revela confusão mental leve e dificuldade de concentração.
    """
    
    analysis_response = analysis_agent.analyze_symptoms(sample_text)
    print(f"Análise: {analysis_response.content[:200]}...")
    print(f"Confiança: {analysis_response.confidence:.2f}")
    
    # Agente de Síntese
    print("\n📋 Testando Agente de Síntese...")
    synthesis_agent = SynthesisAgent()
    
    synthesis_response = synthesis_agent.synthesize_multi_agent_responses([
        search_response, analysis_response
    ])
    print(f"Síntese: {synthesis_response.content[:200]}...")
    print(f"Confiança: {synthesis_response.confidence:.2f}")

def demo_pdf_processing():
    """Demonstração do processamento de PDF"""
    print("\n📄 Demo: Processamento de PDF")
    print("=" * 30)
    
    pdf_processor = PDFProcessor()
    
    # Simular processamento (sem arquivo real)
    print("📝 Simulando processamento de PDF...")
    print("- Extração de texto: ✅")
    print("- Criação de chunks: ✅")
    print("- Geração de embeddings: ✅")
    print("- Indexação no banco vetorial: ✅")
    
    # Mostrar estatísticas simuladas
    print("\n📊 Estatísticas simuladas:")
    print("- Páginas processadas: 15")
    print("- Chunks criados: 45")
    print("- Tempo de processamento: 2.3s")

def demo_clinical_report():
    """Demonstração de relatório clínico"""
    print("\n🏥 Demo: Relatório Clínico")
    print("=" * 30)
    
    vector_store = VectorStore()
    coordinator = CoordinatorAgent(vector_store)
    
    # Dados simulados do paciente
    patient_data = {
        "id": "PAC001",
        "idade": 45,
        "sexo": "Feminino",
        "gravidade_covid": "Moderada",
        "duracao_sintomas": 21
    }
    
    symptoms_description = """
    Paciente relata cefaleia persistente, perda de olfato e paladar, 
    episódios de confusão mental e dificuldade de concentração. 
    Sintomas iniciaram durante infecção aguda por COVID-19 e persistem 
    há 3 semanas após recuperação.
    """
    
    print("👤 Dados do paciente:")
    for key, value in patient_data.items():
        print(f"  {key}: {value}")
    
    print(f"\n🩺 Sintomas: {symptoms_description}")
    
    try:
        query = f"Gere um relatório clínico para: {symptoms_description}"
        response = coordinator.process_user_query(query, {"patient_data": patient_data})
        
        print(f"\n📋 Relatório gerado por {response.agent_name}:")
        print(f"Confiança: {response.confidence:.2f}")
        print(f"Conteúdo: {response.content[:300]}...")
        
    except Exception as e:
        print(f"Erro ao gerar relatório: {e}")

def demo_system_statistics():
    """Demonstração das estatísticas do sistema"""
    print("\n📊 Demo: Estatísticas do Sistema")
    print("=" * 35)
    
    vector_store = VectorStore()
    coordinator = CoordinatorAgent(vector_store)
    
    try:
        stats = coordinator.get_system_statistics()
        
        print("🗄️ Banco de Dados:")
        db_stats = stats.get("database_stats", {})
        print(f"  Total de chunks: {db_stats.get('total_chunks', 0)}")
        print(f"  Arquivos únicos: {db_stats.get('unique_files', 0)}")
        
        print("\n🤖 Status dos Agentes:")
        agents_status = stats.get("agents_status", {})
        for agent_name, agent_info in agents_status.items():
            print(f"  {agent_name}: {agent_info.get('name', 'N/A')}")
        
        print("\n📈 Interações:")
        interaction_stats = stats.get("interaction_stats", {})
        print(f"  Total: {interaction_stats.get('total_interactions', 0)}")
        print(f"  Taxa de sucesso: {interaction_stats.get('success_rate', 0):.1%}")
        
    except Exception as e:
        print(f"Erro ao obter estatísticas: {e}")

def main():
    """Função principal da demonstração"""
    print("🎯 Iniciando demonstração completa do sistema...")
    
    try:
        # Executar todas as demonstrações
        demo_basic_usage()
        demo_individual_agents()
        demo_pdf_processing()
        demo_clinical_report()
        demo_system_statistics()
        
        print("\n✅ Demonstração concluída com sucesso!")
        print("\n💡 Para usar o sistema completo, execute:")
        print("   python run_system.py")
        print("   ou")
        print("   streamlit run app.py")
        
    except Exception as e:
        print(f"\n❌ Erro durante demonstração: {e}")
        print("\n🔧 Verifique se:")
        print("  - Ollama está rodando (ollama serve)")
        print("  - Modelos estão instalados (ollama pull llama2:7b)")
        print("  - Dependências estão instaladas (pip install -r requirements.txt)")

if __name__ == "__main__":
    main()
