"""
Agente coordenador do sistema de multiagentes
"""
from typing import List, Dict, Any, Optional, Tuple
import logging
import asyncio
from enum import Enum

from agents.base_agent import BaseAgent, AgentResponse
from agents.search_agent import SearchAgent
from agents.analysis_agent import AnalysisAgent
from agents.synthesis_agent import SynthesisAgent
from database.vector_store import VectorStore
from config.settings import SYSTEM_PROMPTS

class QueryType(Enum):
    """Tipos de consulta que o sistema pode processar"""
    SEARCH = "search"
    ANALYSIS = "analysis"
    SYNTHESIS = "synthesis"
    COMPARATIVE = "comparative"
    CLINICAL = "clinical"
    RESEARCH = "research"

class CoordinatorAgent(BaseAgent):
    """Agente coordenador que orquestra outros agentes"""
    
    def __init__(self, vector_store: VectorStore, model: str = None):
        super().__init__("coordinator_agent", model)
        self.vector_store = vector_store
        self.logger = logging.getLogger(__name__)
        
        # Inicializar agentes especializados
        self.search_agent = SearchAgent(vector_store, model)
        self.analysis_agent = AnalysisAgent(model)
        self.synthesis_agent = SynthesisAgent(model)
        
        # Histórico de interações
        self.interaction_history = []
    
    def get_system_prompt(self) -> str:
        """Retorna o prompt do sistema para coordenação"""
        return SYSTEM_PROMPTS["coordinator_agent"]
    
    def process_user_query(self, user_query: str, context: Dict[str, Any] = None) -> AgentResponse:
        """Processa consulta do usuário coordenando agentes apropriados"""
        try:
            self.logger.info(f"Processando consulta: {user_query}")
            
            # Determinar tipo de consulta e estratégia
            query_type, strategy = self._determine_query_strategy(user_query, context)
            
            # Executar estratégia apropriada
            if query_type == QueryType.SEARCH:
                response = self._handle_search_query(user_query, context)
            elif query_type == QueryType.ANALYSIS:
                response = self._handle_analysis_query(user_query, context)
            elif query_type == QueryType.SYNTHESIS:
                response = self._handle_synthesis_query(user_query, context)
            elif query_type == QueryType.COMPARATIVE:
                response = self._handle_comparative_query(user_query, context)
            elif query_type == QueryType.CLINICAL:
                response = self._handle_clinical_query(user_query, context)
            elif query_type == QueryType.RESEARCH:
                response = self._handle_research_query(user_query, context)
            else:
                response = self._handle_general_query(user_query, context)
            
            # Registrar interação
            self._record_interaction(user_query, response, query_type)
            
            return response
            
        except Exception as e:
            self.logger.error(f"Erro ao processar consulta: {e}")
            return AgentResponse(
                content=f"Erro ao processar sua consulta: {str(e)}",
                agent_name=self.name,
                confidence=0.0,
                metadata={"error": str(e)}
            )
    
    def _determine_query_strategy(self, query: str, context: Dict[str, Any] = None) -> Tuple[QueryType, str]:
        """Determina o tipo de consulta e estratégia de processamento"""
        query_lower = query.lower()
        
        # Palavras-chave para diferentes tipos de consulta
        search_keywords = ["buscar", "encontrar", "procurar", "artigos", "documentos", "pesquisar"]
        analysis_keywords = ["analisar", "sintomas", "tratamento", "diagnóstico", "avaliar"]
        synthesis_keywords = ["resumir", "sintetizar", "compilar", "integrar", "consolidar"]
        comparative_keywords = ["comparar", "diferenças", "semelhanças", "versus", "vs"]
        clinical_keywords = ["paciente", "caso clínico", "relatório", "prontuário"]
        research_keywords = ["pesquisa", "estudo", "evidências", "literatura", "revisão"]
        
        # Determinar tipo baseado em palavras-chave
        if any(keyword in query_lower for keyword in search_keywords):
            return QueryType.SEARCH, "search_documents"
        elif any(keyword in query_lower for keyword in analysis_keywords):
            return QueryType.ANALYSIS, "analyze_medical_content"
        elif any(keyword in query_lower for keyword in synthesis_keywords):
            return QueryType.SYNTHESIS, "synthesize_information"
        elif any(keyword in query_lower for keyword in comparative_keywords):
            return QueryType.COMPARATIVE, "compare_studies"
        elif any(keyword in query_lower for keyword in clinical_keywords):
            return QueryType.CLINICAL, "clinical_analysis"
        elif any(keyword in query_lower for keyword in research_keywords):
            return QueryType.RESEARCH, "research_summary"
        else:
            return QueryType.SEARCH, "general_search"  # Default para busca
    
    def _handle_search_query(self, query: str, context: Dict[str, Any] = None) -> AgentResponse:
        """Processa consultas de busca"""
        # Buscar documentos relevantes
        search_response = self.search_agent.search_documents(query, n_results=5)
        
        if search_response.confidence > 0.5:
            return search_response
        else:
            # Se busca não foi bem-sucedida, tentar busca mais ampla
            broader_query = f"manifestações neurológicas COVID-19 {query}"
            return self.search_agent.search_documents(broader_query, n_results=3)
    
    def _handle_analysis_query(self, query: str, context: Dict[str, Any] = None) -> AgentResponse:
        """Processa consultas de análise médica"""
        # Primeiro buscar documentos relevantes
        search_response = self.search_agent.search_documents(query, n_results=3)
        
        if search_response.metadata.get("results_count", 0) > 0:
            # Usar documentos encontrados para análise
            documents = search_response.metadata.get("search_results", [])
            combined_text = " ".join([doc.get("content_preview", "") for doc in documents])
            
            # Analisar com base no tipo de consulta
            if "sintoma" in query.lower():
                return self.analysis_agent.analyze_symptoms(combined_text)
            elif "tratamento" in query.lower():
                return self.analysis_agent.analyze_treatment_options(combined_text)
            else:
                # Análise geral
                return self.analysis_agent.generate_medical_summary([{"content": combined_text}])
        else:
            return AgentResponse(
                content="Não foram encontrados documentos suficientes para análise médica.",
                agent_name=self.name,
                confidence=0.0
            )
    
    def _handle_synthesis_query(self, query: str, context: Dict[str, Any] = None) -> AgentResponse:
        """Processa consultas de síntese"""
        # Buscar múltiplos documentos
        search_response = self.search_agent.search_documents(query, n_results=5)
        
        if search_response.metadata.get("results_count", 0) >= 2:
            # Obter análises de diferentes agentes
            documents = search_response.metadata.get("search_results", [])
            
            # Análise médica
            analysis_response = self.analysis_agent.generate_medical_summary(documents)
            
            # Sintetizar respostas
            return self.synthesis_agent.synthesize_multi_agent_responses([
                search_response, analysis_response
            ])
        else:
            return AgentResponse(
                content="Documentos insuficientes para síntese abrangente.",
                agent_name=self.name,
                confidence=0.0
            )
    
    def _handle_comparative_query(self, query: str, context: Dict[str, Any] = None) -> AgentResponse:
        """Processa consultas comparativas"""
        # Buscar documentos para comparação
        search_response = self.search_agent.search_documents(query, n_results=6)
        
        if search_response.metadata.get("results_count", 0) >= 2:
            documents = search_response.metadata.get("search_results", [])
            
            # Dividir documentos em grupos para comparação
            group1 = documents[:len(documents)//2]
            group2 = documents[len(documents)//2:]
            
            # Analisar cada grupo
            analysis1 = self.analysis_agent.generate_medical_summary(group1)
            analysis2 = self.analysis_agent.generate_medical_summary(group2)
            
            # Criar análise comparativa
            return self.synthesis_agent.create_comparative_analysis(
                query, [analysis1, analysis2]
            )
        else:
            return AgentResponse(
                content="Documentos insuficientes para análise comparativa.",
                agent_name=self.name,
                confidence=0.0
            )
    
    def _handle_clinical_query(self, query: str, context: Dict[str, Any] = None) -> AgentResponse:
        """Processa consultas clínicas"""
        # Extrair dados do paciente do contexto
        patient_data = context.get("patient_data", {}) if context else {}
        
        # Buscar informações relevantes
        search_response = self.search_agent.search_documents(query, n_results=4)
        
        # Análise médica específica
        if search_response.metadata.get("results_count", 0) > 0:
            documents = search_response.metadata.get("search_results", [])
            analysis_response = self.analysis_agent.generate_medical_summary(documents)
            
            # Gerar relatório clínico
            return self.synthesis_agent.generate_clinical_report(
                patient_data, [search_response, analysis_response]
            )
        else:
            return AgentResponse(
                content="Informações insuficientes para análise clínica.",
                agent_name=self.name,
                confidence=0.0
            )
    
    def _handle_research_query(self, query: str, context: Dict[str, Any] = None) -> AgentResponse:
        """Processa consultas de pesquisa"""
        # Buscar artigos científicos
        search_response = self.search_agent.search_documents(query, n_results=8)
        
        if search_response.metadata.get("results_count", 0) >= 3:
            documents = search_response.metadata.get("search_results", [])
            
            # Gerar resumo de pesquisa
            return self.synthesis_agent.generate_research_summary(documents, query)
        else:
            return AgentResponse(
                content="Artigos insuficientes para resumo de pesquisa abrangente.",
                agent_name=self.name,
                confidence=0.0
            )
    
    def _handle_general_query(self, query: str, context: Dict[str, Any] = None) -> AgentResponse:
        """Processa consultas gerais"""
        # Estratégia padrão: busca + análise básica
        search_response = self.search_agent.search_documents(query, n_results=3)
        
        if search_response.confidence > 0.3:
            return search_response
        else:
            # Resposta geral usando conhecimento do modelo
            general_response = self.generate_response(
                f"Responda sobre neurocovid: {query}"
            )
            
            return AgentResponse(
                content=general_response,
                agent_name=self.name,
                confidence=0.6,
                metadata={"query_type": "general"}
            )
    
    def _record_interaction(self, query: str, response: AgentResponse, query_type: QueryType):
        """Registra interação para histórico"""
        interaction = {
            "timestamp": response.timestamp,
            "query": query,
            "query_type": query_type.value,
            "response_agent": response.agent_name,
            "confidence": response.confidence,
            "success": response.confidence > 0.5
        }
        
        self.interaction_history.append(interaction)
        
        # Manter apenas últimas 100 interações
        if len(self.interaction_history) > 100:
            self.interaction_history = self.interaction_history[-100:]
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """Retorna estatísticas do sistema"""
        # Estatísticas do banco de dados
        db_stats = self.search_agent.get_search_statistics()
        
        # Estatísticas de interações
        total_interactions = len(self.interaction_history)
        successful_interactions = sum(1 for i in self.interaction_history if i["success"])
        
        query_types = {}
        for interaction in self.interaction_history:
            qt = interaction["query_type"]
            query_types[qt] = query_types.get(qt, 0) + 1
        
        return {
            "database_stats": db_stats,
            "interaction_stats": {
                "total_interactions": total_interactions,
                "successful_interactions": successful_interactions,
                "success_rate": successful_interactions / total_interactions if total_interactions > 0 else 0,
                "query_types_distribution": query_types
            },
            "agents_status": {
                "search_agent": self.search_agent.get_agent_info(),
                "analysis_agent": self.analysis_agent.get_agent_info(),
                "synthesis_agent": self.synthesis_agent.get_agent_info(),
                "coordinator_agent": self.get_agent_info()
            }
        }
