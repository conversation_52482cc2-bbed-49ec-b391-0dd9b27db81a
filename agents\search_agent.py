"""
Agente especializado em busca e indexação de documentos
"""
from typing import List, Dict, Any, Optional
import logging

from agents.base_agent import BaseAgent, AgentResponse
from database.vector_store import VectorStore
from config.settings import SYSTEM_PROMPTS

class SearchAgent(BaseAgent):
    """Agente responsável por busca e recuperação de documentos"""
    
    def __init__(self, vector_store: VectorStore, model: str = None):
        super().__init__("search_agent", model)
        self.vector_store = vector_store
        self.logger = logging.getLogger(__name__)
    
    def get_system_prompt(self) -> str:
        """Retorna o prompt do sistema para o agente de busca"""
        return SYSTEM_PROMPTS["search_agent"]
    
    def search_documents(self, query: str, n_results: int = 5, 
                        filter_metadata: Dict[str, Any] = None) -> AgentResponse:
        """Busca documentos relevantes para a consulta"""
        try:
            self.logger.info(f"Buscando documentos para: {query}")
            
            # Realizar busca no banco vetorial
            results = self.vector_store.search_similar(
                query=query,
                n_results=n_results,
                filter_metadata=filter_metadata
            )
            
            if not results:
                response_content = "Não foram encontrados documentos relevantes para sua consulta."
                return AgentResponse(
                    content=response_content,
                    agent_name=self.name,
                    confidence=0.0,
                    metadata={"results_count": 0}
                )
            
            # Processar resultados
            processed_results = self._process_search_results(results, query)
            
            # Gerar resposta contextualizada
            context = self._build_context_from_results(results)
            response_content = self.generate_response(
                f"Com base nos documentos encontrados, responda à consulta: {query}",
                context=context
            )
            
            return AgentResponse(
                content=response_content,
                agent_name=self.name,
                confidence=self._calculate_confidence(results),
                metadata={
                    "results_count": len(results),
                    "search_results": processed_results,
                    "query": query
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro na busca: {e}")
            return AgentResponse(
                content=f"Erro ao realizar busca: {str(e)}",
                agent_name=self.name,
                confidence=0.0,
                metadata={"error": str(e)}
            )
    
    def _process_search_results(self, results: List[Dict[str, Any]], 
                               query: str) -> List[Dict[str, Any]]:
        """Processa e formata resultados da busca"""
        processed = []
        
        for result in results:
            processed.append({
                "content_preview": result["content"][:300] + "...",
                "file_name": result["metadata"].get("file_name", ""),
                "page_number": result["metadata"].get("page_number", 0),
                "similarity_score": round(result["similarity_score"], 3),
                "chunk_id": result["chunk_id"]
            })
        
        return processed
    
    def _build_context_from_results(self, results: List[Dict[str, Any]]) -> str:
        """Constrói contexto a partir dos resultados da busca"""
        context_parts = []
        
        for i, result in enumerate(results[:3]):  # Top 3 resultados
            context_parts.append(
                f"Documento {i+1} (Arquivo: {result['metadata'].get('file_name', 'N/A')}, "
                f"Página: {result['metadata'].get('page_number', 'N/A')}):\n"
                f"{result['content']}\n"
            )
        
        return "\n".join(context_parts)
    
    def _calculate_confidence(self, results: List[Dict[str, Any]]) -> float:
        """Calcula nível de confiança baseado nos resultados"""
        if not results:
            return 0.0
        
        # Média dos scores de similaridade
        avg_similarity = sum(r["similarity_score"] for r in results) / len(results)
        
        # Ajustar baseado na quantidade de resultados
        quantity_factor = min(len(results) / 5, 1.0)
        
        return round(avg_similarity * quantity_factor, 2)
    
    def get_document_details(self, chunk_id: str) -> AgentResponse:
        """Obtém detalhes completos de um documento específico"""
        try:
            document = self.vector_store.get_document_by_id(chunk_id)
            
            if not document:
                return AgentResponse(
                    content="Documento não encontrado.",
                    agent_name=self.name,
                    confidence=0.0
                )
            
            # Gerar análise do documento
            analysis = self.generate_response(
                f"Analise este trecho de documento médico sobre neurocovid: {document['content']}"
            )
            
            return AgentResponse(
                content=analysis,
                agent_name=self.name,
                confidence=1.0,
                metadata={
                    "document": document,
                    "chunk_id": chunk_id
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro ao obter detalhes do documento: {e}")
            return AgentResponse(
                content=f"Erro ao acessar documento: {str(e)}",
                agent_name=self.name,
                confidence=0.0
            )
    
    def suggest_related_queries(self, original_query: str) -> AgentResponse:
        """Sugere consultas relacionadas baseadas na consulta original"""
        try:
            # Buscar documentos relacionados
            results = self.vector_store.search_similar(original_query, n_results=3)
            
            if not results:
                return AgentResponse(
                    content="Não foi possível gerar sugestões relacionadas.",
                    agent_name=self.name,
                    confidence=0.0
                )
            
            # Gerar sugestões baseadas no conteúdo encontrado
            context = self._build_context_from_results(results)
            suggestions = self.generate_response(
                f"Com base no conteúdo sobre neurocovid encontrado, sugira 5 consultas "
                f"relacionadas à consulta original: '{original_query}'. "
                f"Formate como uma lista numerada.",
                context=context
            )
            
            return AgentResponse(
                content=suggestions,
                agent_name=self.name,
                confidence=0.8,
                metadata={
                    "original_query": original_query,
                    "based_on_documents": len(results)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro ao gerar sugestões: {e}")
            return AgentResponse(
                content="Erro ao gerar sugestões relacionadas.",
                agent_name=self.name,
                confidence=0.0
            )
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """Retorna estatísticas do banco de dados"""
        try:
            stats = self.vector_store.get_collection_stats()
            return {
                "total_documents": stats["total_chunks"],
                "unique_files": stats["unique_files"],
                "available_files": stats["files"],
                "agent_info": self.get_agent_info()
            }
        except Exception as e:
            self.logger.error(f"Erro ao obter estatísticas: {e}")
            return {"error": str(e)}
