"""
Interface principal do sistema de multiagentes para análise de neurocovid
"""
import streamlit as st
import os
import logging
from pathlib import Path
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import List, Dict, Any
import time

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Imports do sistema
from config.settings import STREAMLIT_CONFIG, PDF_CONFIG, DATA_DIR, PDF_DIR
from database.vector_store import VectorStore
from utils.pdf_processor import PDFProcessor, validate_pdf
from agents.coordinator_agent import CoordinatorAgent

# Configuração da página
st.set_page_config(
    page_title=STREAMLIT_CONFIG["page_title"],
    page_icon=STREAMLIT_CONFIG["page_icon"],
    layout=STREAMLIT_CONFIG["layout"],
    initial_sidebar_state=STREAMLIT_CONFIG["initial_sidebar_state"]
)

# CSS customizado
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .agent-response {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        border-left: 4px solid #1f77b4;
    }
    .confidence-high { border-left-color: #28a745; }
    .confidence-medium { border-left-color: #ffc107; }
    .confidence-low { border-left-color: #dc3545; }
    .sidebar-section {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def initialize_system():
    """Inicializa o sistema de multiagentes"""
    try:
        # Inicializar banco vetorial
        vector_store = VectorStore()
        
        # Inicializar processador de PDF
        pdf_processor = PDFProcessor()
        
        # Inicializar agente coordenador
        coordinator = CoordinatorAgent(vector_store)
        
        return vector_store, pdf_processor, coordinator
    except Exception as e:
        st.error(f"Erro ao inicializar sistema: {e}")
        return None, None, None

def main():
    """Função principal da aplicação"""
    
    # Header principal
    st.markdown('<h1 class="main-header">🧠 Sistema de Análise Neurocovid</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Inicializar sistema
    vector_store, pdf_processor, coordinator = initialize_system()
    
    if not all([vector_store, pdf_processor, coordinator]):
        st.error("Falha na inicialização do sistema. Verifique as configurações.")
        return
    
    # Sidebar
    with st.sidebar:
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.header("📁 Gerenciamento de Documentos")
        
        # Upload de PDFs
        uploaded_files = st.file_uploader(
            "Carregar artigos PDF",
            type=["pdf"],
            accept_multiple_files=True,
            help="Carregue artigos científicos sobre neurocovid"
        )
        
        if uploaded_files:
            if st.button("Processar PDFs"):
                process_uploaded_files(uploaded_files, pdf_processor, vector_store)
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Estatísticas do sistema
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.header("📊 Estatísticas")
        display_system_stats(coordinator)
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Configurações
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.header("⚙️ Configurações")
        
        # Seleção de modelo
        model_options = ["llama2:7b", "llama2:13b", "mistral:7b", "codellama:7b"]
        selected_model = st.selectbox("Modelo Ollama", model_options)
        
        # Número de resultados
        n_results = st.slider("Resultados por busca", 1, 10, 5)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Área principal
    tab1, tab2, tab3, tab4 = st.tabs(["💬 Chat Multimodal", "🔍 Busca Avançada", "📈 Análise", "📋 Relatórios"])
    
    with tab1:
        chat_interface(coordinator, n_results)
    
    with tab2:
        advanced_search_interface(vector_store, coordinator)
    
    with tab3:
        analysis_interface(coordinator)
    
    with tab4:
        reports_interface(coordinator)

def process_uploaded_files(uploaded_files: List, pdf_processor: PDFProcessor, vector_store: VectorStore):
    """Processa arquivos PDF carregados"""
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    total_files = len(uploaded_files)
    processed_chunks = 0
    
    for i, uploaded_file in enumerate(uploaded_files):
        status_text.text(f"Processando {uploaded_file.name}...")
        
        # Salvar arquivo temporariamente
        temp_path = PDF_DIR / uploaded_file.name
        with open(temp_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        # Validar PDF
        if not validate_pdf(str(temp_path)):
            st.error(f"Arquivo inválido: {uploaded_file.name}")
            continue
        
        # Processar PDF
        chunks = pdf_processor.process_pdf(str(temp_path))
        
        if chunks:
            # Adicionar ao banco vetorial
            success = vector_store.add_documents(chunks)
            if success:
                processed_chunks += len(chunks)
                st.success(f"✅ {uploaded_file.name}: {len(chunks)} chunks processados")
            else:
                st.error(f"❌ Erro ao indexar {uploaded_file.name}")
        else:
            st.error(f"❌ Não foi possível extrair texto de {uploaded_file.name}")
        
        # Atualizar progresso
        progress_bar.progress((i + 1) / total_files)
    
    status_text.text(f"Processamento concluído! {processed_chunks} chunks adicionados ao banco.")
    time.sleep(2)
    status_text.empty()
    progress_bar.empty()

def chat_interface(coordinator: CoordinatorAgent, n_results: int):
    """Interface de chat multimodal"""
    st.header("💬 Chat com Multiagentes")
    
    # Inicializar histórico de chat
    if "chat_history" not in st.session_state:
        st.session_state.chat_history = []
    
    # Área de chat
    chat_container = st.container()
    
    # Input do usuário
    user_input = st.text_area(
        "Sua pergunta sobre neurocovid:",
        placeholder="Ex: Quais são as principais manifestações neurológicas da COVID-19?",
        height=100
    )
    
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        if st.button("Enviar", type="primary"):
            if user_input.strip():
                process_chat_message(user_input, coordinator, n_results)
    
    with col2:
        if st.button("Limpar Chat"):
            st.session_state.chat_history = []
            st.rerun()
    
    # Exibir histórico de chat
    with chat_container:
        for message in st.session_state.chat_history:
            if message["role"] == "user":
                st.markdown(f"**👤 Você:** {message['content']}")
            else:
                confidence_class = get_confidence_class(message.get("confidence", 0))
                st.markdown(f"""
                <div class="agent-response {confidence_class}">
                    <strong>🤖 {message['agent']}:</strong><br>
                    {message['content']}<br>
                    <small>Confiança: {message.get('confidence', 0):.2f}</small>
                </div>
                """, unsafe_allow_html=True)

def process_chat_message(user_input: str, coordinator: CoordinatorAgent, n_results: int):
    """Processa mensagem do chat"""
    # Adicionar mensagem do usuário
    st.session_state.chat_history.append({
        "role": "user",
        "content": user_input
    })
    
    # Processar com coordenador
    with st.spinner("Processando com multiagentes..."):
        response = coordinator.process_user_query(user_input)
    
    # Adicionar resposta do agente
    st.session_state.chat_history.append({
        "role": "assistant",
        "agent": response.agent_name,
        "content": response.content,
        "confidence": response.confidence,
        "metadata": response.metadata
    })
    
    st.rerun()

def advanced_search_interface(vector_store: VectorStore, coordinator: CoordinatorAgent):
    """Interface de busca avançada"""
    st.header("🔍 Busca Avançada")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        search_query = st.text_input("Termo de busca:", placeholder="Ex: anosmia COVID-19")
    
    with col2:
        search_results_count = st.selectbox("Número de resultados:", [3, 5, 10, 15])
    
    if st.button("Buscar") and search_query:
        with st.spinner("Buscando documentos..."):
            results = vector_store.search_similar(search_query, n_results=search_results_count)
        
        if results:
            st.success(f"Encontrados {len(results)} documentos relevantes")
            
            for i, result in enumerate(results):
                with st.expander(f"Documento {i+1} - {result['metadata'].get('file_name', 'N/A')} (Similaridade: {result['similarity_score']:.3f})"):
                    st.write(f"**Página:** {result['metadata'].get('page_number', 'N/A')}")
                    st.write(f"**Conteúdo:**")
                    st.write(result['content'][:1000] + "..." if len(result['content']) > 1000 else result['content'])
        else:
            st.warning("Nenhum documento encontrado para esta busca.")

def analysis_interface(coordinator: CoordinatorAgent):
    """Interface de análise"""
    st.header("📈 Análise Especializada")
    
    analysis_type = st.selectbox(
        "Tipo de análise:",
        ["Análise de Sintomas", "Análise de Tratamentos", "Comparação de Estudos", "Síntese de Literatura"]
    )
    
    analysis_input = st.text_area(
        "Texto para análise:",
        placeholder="Cole aqui o texto médico para análise...",
        height=200
    )
    
    if st.button("Analisar") and analysis_input:
        with st.spinner("Realizando análise especializada..."):
            if analysis_type == "Análise de Sintomas":
                query = f"Analise os sintomas neurológicos: {analysis_input}"
            elif analysis_type == "Análise de Tratamentos":
                query = f"Analise as opções de tratamento: {analysis_input}"
            elif analysis_type == "Comparação de Estudos":
                query = f"Compare os estudos: {analysis_input}"
            else:
                query = f"Sintetize a literatura sobre: {analysis_input}"
            
            response = coordinator.process_user_query(query)
        
        confidence_class = get_confidence_class(response.confidence)
        st.markdown(f"""
        <div class="agent-response {confidence_class}">
            <strong>Análise por {response.agent_name}:</strong><br>
            {response.content}<br>
            <small>Confiança: {response.confidence:.2f}</small>
        </div>
        """, unsafe_allow_html=True)

def reports_interface(coordinator: CoordinatorAgent):
    """Interface de relatórios"""
    st.header("📋 Geração de Relatórios")
    
    report_type = st.selectbox(
        "Tipo de relatório:",
        ["Relatório Clínico", "Resumo de Pesquisa", "Análise Comparativa"]
    )
    
    if report_type == "Relatório Clínico":
        st.subheader("Dados do Paciente")
        col1, col2 = st.columns(2)
        
        with col1:
            patient_age = st.number_input("Idade", min_value=0, max_value=120, value=45)
            patient_gender = st.selectbox("Sexo", ["Masculino", "Feminino", "Outro"])
        
        with col2:
            covid_severity = st.selectbox("Gravidade COVID-19", ["Leve", "Moderada", "Grave", "Crítica"])
            symptoms_duration = st.number_input("Duração dos sintomas (dias)", min_value=0, value=14)
        
        symptoms_description = st.text_area("Descrição dos sintomas neurológicos:")
        
        if st.button("Gerar Relatório Clínico") and symptoms_description:
            patient_data = {
                "idade": patient_age,
                "sexo": patient_gender,
                "gravidade_covid": covid_severity,
                "duracao_sintomas": symptoms_duration
            }
            
            with st.spinner("Gerando relatório clínico..."):
                query = f"Gere um relatório clínico para paciente com: {symptoms_description}"
                response = coordinator.process_user_query(query, {"patient_data": patient_data})
            
            st.markdown("### Relatório Clínico Gerado")
            st.markdown(response.content)
    
    elif report_type == "Resumo de Pesquisa":
        research_question = st.text_input("Pergunta de pesquisa:")
        
        if st.button("Gerar Resumo") and research_question:
            with st.spinner("Gerando resumo de pesquisa..."):
                query = f"Gere um resumo de pesquisa sobre: {research_question}"
                response = coordinator.process_user_query(query)
            
            st.markdown("### Resumo de Pesquisa")
            st.markdown(response.content)

def display_system_stats(coordinator: CoordinatorAgent):
    """Exibe estatísticas do sistema"""
    try:
        stats = coordinator.get_system_statistics()
        
        # Estatísticas do banco de dados
        db_stats = stats.get("database_stats", {})
        st.metric("Documentos", db_stats.get("total_chunks", 0))
        st.metric("Arquivos únicos", db_stats.get("unique_files", 0))
        
        # Estatísticas de interações
        interaction_stats = stats.get("interaction_stats", {})
        if interaction_stats.get("total_interactions", 0) > 0:
            st.metric("Taxa de sucesso", f"{interaction_stats.get('success_rate', 0):.1%}")
        
    except Exception as e:
        st.error(f"Erro ao carregar estatísticas: {e}")

def get_confidence_class(confidence: float) -> str:
    """Retorna classe CSS baseada no nível de confiança"""
    if confidence >= 0.7:
        return "confidence-high"
    elif confidence >= 0.4:
        return "confidence-medium"
    else:
        return "confidence-low"

if __name__ == "__main__":
    main()
