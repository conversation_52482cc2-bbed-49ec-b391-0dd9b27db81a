"""
Agente especializado em análise de conteúdo médico sobre neurocovid
"""
from typing import List, Dict, Any, Optional
import logging
import re

from agents.base_agent import BaseAgent, AgentResponse
from config.settings import SYSTEM_PROMPTS

class AnalysisAgent(BaseAgent):
    """Agente responsável por análise médica especializada"""
    
    def __init__(self, model: str = None):
        super().__init__("analysis_agent", model)
        self.logger = logging.getLogger(__name__)
        
        # Termos médicos relevantes para neurocovid
        self.medical_terms = {
            "symptoms": ["cefaleia", "anosmia", "ageusia", "confusão mental", "delirium", 
                        "convulsões", "AVC", "encefalite", "meningite", "neuropatia"],
            "conditions": ["covid-19", "sars-cov-2", "neurocovid", "manifestações neurológicas",
                          "síndrome pós-covid", "covid longo", "long covid"],
            "treatments": ["corticosteroides", "antivirais", "neuroproteção", "reabilitação",
                          "fisioterapia", "terapia ocupacional"]
        }
    
    def get_system_prompt(self) -> str:
        """Retorna o prompt do sistema para análise médica"""
        return SYSTEM_PROMPTS["analysis_agent"]
    
    def analyze_symptoms(self, text: str, patient_context: str = None) -> AgentResponse:
        """Analisa sintomas neurológicos mencionados no texto"""
        try:
            # Identificar sintomas mencionados
            found_symptoms = self._extract_medical_terms(text, "symptoms")
            
            # Preparar contexto para análise
            analysis_prompt = f"""
            Analise os seguintes sintomas neurológicos relacionados à COVID-19:
            
            Texto: {text}
            
            Sintomas identificados: {', '.join(found_symptoms) if found_symptoms else 'Nenhum sintoma específico identificado'}
            
            {f'Contexto do paciente: {patient_context}' if patient_context else ''}
            
            Forneça uma análise médica detalhada incluindo:
            1. Interpretação dos sintomas
            2. Possíveis mecanismos fisiopatológicos
            3. Correlação com manifestações neurológicas da COVID-19
            4. Recomendações para investigação adicional
            """
            
            analysis = self.generate_response(analysis_prompt)
            
            return AgentResponse(
                content=analysis,
                agent_name=self.name,
                confidence=self._calculate_medical_confidence(found_symptoms, text),
                metadata={
                    "identified_symptoms": found_symptoms,
                    "analysis_type": "symptom_analysis",
                    "has_patient_context": bool(patient_context)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro na análise de sintomas: {e}")
            return AgentResponse(
                content=f"Erro ao analisar sintomas: {str(e)}",
                agent_name=self.name,
                confidence=0.0
            )
    
    def analyze_treatment_options(self, condition_description: str, 
                                 patient_profile: str = None) -> AgentResponse:
        """Analisa opções de tratamento baseadas na condição descrita"""
        try:
            # Identificar tratamentos mencionados
            found_treatments = self._extract_medical_terms(condition_description, "treatments")
            
            treatment_prompt = f"""
            Analise as opções de tratamento para manifestações neurológicas da COVID-19:
            
            Descrição da condição: {condition_description}
            
            Tratamentos mencionados: {', '.join(found_treatments) if found_treatments else 'Nenhum tratamento específico mencionado'}
            
            {f'Perfil do paciente: {patient_profile}' if patient_profile else ''}
            
            Forneça uma análise abrangente incluindo:
            1. Eficácia dos tratamentos mencionados
            2. Alternativas terapêuticas baseadas em evidências
            3. Considerações sobre efeitos colaterais
            4. Protocolos de monitoramento
            5. Prognóstico esperado
            """
            
            analysis = self.generate_response(treatment_prompt)
            
            return AgentResponse(
                content=analysis,
                agent_name=self.name,
                confidence=self._calculate_medical_confidence(found_treatments, condition_description),
                metadata={
                    "identified_treatments": found_treatments,
                    "analysis_type": "treatment_analysis",
                    "has_patient_profile": bool(patient_profile)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro na análise de tratamentos: {e}")
            return AgentResponse(
                content=f"Erro ao analisar tratamentos: {str(e)}",
                agent_name=self.name,
                confidence=0.0
            )
    
    def compare_studies(self, study_texts: List[str]) -> AgentResponse:
        """Compara múltiplos estudos sobre neurocovid"""
        try:
            if len(study_texts) < 2:
                return AgentResponse(
                    content="São necessários pelo menos 2 estudos para comparação.",
                    agent_name=self.name,
                    confidence=0.0
                )
            
            # Analisar cada estudo individualmente
            study_analyses = []
            for i, study in enumerate(study_texts):
                analysis = self._analyze_single_study(study, i + 1)
                study_analyses.append(analysis)
            
            # Gerar comparação
            comparison_prompt = f"""
            Compare os seguintes estudos sobre manifestações neurológicas da COVID-19:
            
            {chr(10).join(study_analyses)}
            
            Forneça uma análise comparativa incluindo:
            1. Semelhanças e diferenças nos achados
            2. Qualidade metodológica dos estudos
            3. Consistência dos resultados
            4. Limitações identificadas
            5. Conclusões integradas
            6. Direções para pesquisas futuras
            """
            
            comparison = self.generate_response(comparison_prompt)
            
            return AgentResponse(
                content=comparison,
                agent_name=self.name,
                confidence=0.9,
                metadata={
                    "studies_compared": len(study_texts),
                    "analysis_type": "comparative_analysis"
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro na comparação de estudos: {e}")
            return AgentResponse(
                content=f"Erro ao comparar estudos: {str(e)}",
                agent_name=self.name,
                confidence=0.0
            )
    
    def _analyze_single_study(self, study_text: str, study_number: int) -> str:
        """Analisa um único estudo"""
        # Extrair informações chave
        symptoms = self._extract_medical_terms(study_text, "symptoms")
        treatments = self._extract_medical_terms(study_text, "treatments")
        
        return f"""
        Estudo {study_number}:
        Sintomas identificados: {', '.join(symptoms) if symptoms else 'Não especificados'}
        Tratamentos mencionados: {', '.join(treatments) if treatments else 'Não especificados'}
        Resumo: {study_text[:500]}...
        """
    
    def _extract_medical_terms(self, text: str, category: str) -> List[str]:
        """Extrai termos médicos específicos do texto"""
        text_lower = text.lower()
        found_terms = []
        
        if category in self.medical_terms:
            for term in self.medical_terms[category]:
                if term.lower() in text_lower:
                    found_terms.append(term)
        
        return found_terms
    
    def _calculate_medical_confidence(self, found_terms: List[str], text: str) -> float:
        """Calcula confiança baseada na presença de termos médicos"""
        base_confidence = 0.5
        
        # Aumentar confiança baseado em termos encontrados
        term_bonus = min(len(found_terms) * 0.1, 0.3)
        
        # Aumentar confiança baseado no tamanho do texto
        length_bonus = min(len(text) / 1000 * 0.1, 0.2)
        
        return min(base_confidence + term_bonus + length_bonus, 1.0)
    
    def generate_medical_summary(self, documents: List[Dict[str, Any]]) -> AgentResponse:
        """Gera resumo médico baseado em múltiplos documentos"""
        try:
            if not documents:
                return AgentResponse(
                    content="Nenhum documento fornecido para resumo.",
                    agent_name=self.name,
                    confidence=0.0
                )
            
            # Compilar conteúdo dos documentos
            combined_content = "\n\n".join([doc.get("content", "") for doc in documents])
            
            summary_prompt = f"""
            Com base nos seguintes documentos sobre neurocovid, gere um resumo médico abrangente:
            
            {combined_content[:4000]}  # Limitar para evitar overflow
            
            O resumo deve incluir:
            1. Principais manifestações neurológicas
            2. Mecanismos fisiopatológicos propostos
            3. Opções terapêuticas disponíveis
            4. Prognóstico e evolução clínica
            5. Lacunas no conhecimento atual
            """
            
            summary = self.generate_response(summary_prompt)
            
            return AgentResponse(
                content=summary,
                agent_name=self.name,
                confidence=0.8,
                metadata={
                    "documents_analyzed": len(documents),
                    "analysis_type": "medical_summary"
                }
            )
            
        except Exception as e:
            self.logger.error(f"Erro ao gerar resumo médico: {e}")
            return AgentResponse(
                content=f"Erro ao gerar resumo: {str(e)}",
                agent_name=self.name,
                confidence=0.0
            )
