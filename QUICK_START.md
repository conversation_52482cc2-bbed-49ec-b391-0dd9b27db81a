# 🚀 Guia de Início Rápido

## Sistema de Multiagentes para Análise de Neurocovid

### ⚡ Instalação Rápida

#### Opção 1: Instalação Automática (Recomendada)
```bash
# Execute o script de instalação automática
python run_system.py
```

#### Opção 2: Instalação Manual
```bash
# 1. Instalar Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 2. Baixar modelos
ollama pull llama2:7b
ollama pull nomic-embed-text

# 3. Instalar dependências Python
pip install -r requirements.txt

# 4. Executar aplicação
streamlit run app.py
```

#### Opção 3: Docker (<PERSON><PERSON> Simples)
```bash
# Construir e executar com Docker Compose
docker-compose up --build

# Acessar: http://localhost:8501
```

### 🎯 Primeiros Passos

1. **Acesse a Interface**
   - Abra http://localhost:8501 no navegador

2. **Carregue Documentos**
   - Use a sidebar "Gerenciamento de Documentos"
   - Faça upload de PDFs sobre neurocovid
   - Clique em "Processar PDFs"

3. **Faça Perguntas**
   - Vá para a aba "Chat Multimodal"
   - Digite perguntas como:
     - "Quais são os sintomas neurológicos da COVID-19?"
     - "Como tratar anosmia pós-COVID?"
     - "Compare estudos sobre encefalite e COVID-19"

### 📋 Exemplos de Uso

#### Consultas de Busca
```
"Buscar artigos sobre manifestações neurológicas"
"Encontrar estudos sobre anosmia"
"Procurar tratamentos para neurocovid"
```

#### Consultas de Análise
```
"Analisar sintomas: cefaleia, anosmia, confusão mental"
"Avaliar tratamento com corticosteroides"
"Examinar caso clínico de encefalite pós-COVID"
```

#### Consultas de Síntese
```
"Resumir literatura sobre neurocovid"
"Sintetizar evidências sobre tratamentos"
"Compilar dados sobre prognóstico"
```

### 🔧 Solução de Problemas

#### Ollama não conecta
```bash
# Verificar se está rodando
ollama list

# Iniciar se necessário
ollama serve
```

#### Modelos não encontrados
```bash
# Baixar modelos necessários
ollama pull llama2:7b
ollama pull nomic-embed-text
```

#### Erro de dependências
```bash
# Reinstalar dependências
pip install -r requirements.txt --force-reinstall
```

### 📊 Funcionalidades Principais

- ✅ **Chat Multimodal**: Converse com múltiplos agentes especializados
- ✅ **Processamento de PDFs**: Upload e indexação automática
- ✅ **Busca Semântica**: Encontre informações relevantes rapidamente
- ✅ **Análise Médica**: Análise especializada de sintomas e tratamentos
- ✅ **Relatórios Clínicos**: Geração automática de relatórios estruturados
- ✅ **Síntese de Literatura**: Compilação inteligente de múltiplas fontes

### 🎮 Demonstração

Execute a demonstração para ver o sistema em ação:
```bash
python examples/demo_usage.py
```

### 🧪 Testes

Execute os testes para verificar se tudo está funcionando:
```bash
python tests/test_system.py
```

### 📞 Suporte

- 📖 Consulte o [README.md](README.md) para documentação completa
- 🐛 Reporte problemas criando uma issue
- 💡 Sugestões são bem-vindas!

---

**🧠 Sistema pronto para analisar neurocovid com IA!**
