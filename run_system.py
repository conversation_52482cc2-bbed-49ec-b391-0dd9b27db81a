"""
Script para inicializar e executar o sistema de multiagentes
"""
import subprocess
import sys
import os
import time
import requests
from pathlib import Path

def check_ollama_connection():
    """Verifica se o Ollama está rodando"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        return response.status_code == 200
    except:
        return False

def check_ollama_models():
    """Verifica se os modelos necessários estão instalados"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json()
            model_names = [model["name"] for model in models.get("models", [])]
            
            required_models = ["llama2:7b", "nomic-embed-text"]
            missing_models = [model for model in required_models if model not in model_names]
            
            return missing_models
        return ["llama2:7b", "nomic-embed-text"]  # Assume todos estão faltando se não conseguir verificar
    except:
        return ["llama2:7b", "nomic-embed-text"]

def install_ollama_models(missing_models):
    """Instala modelos faltantes do Ollama"""
    print("Instalando modelos do Ollama...")
    
    for model in missing_models:
        print(f"Baixando {model}...")
        try:
            result = subprocess.run(
                ["ollama", "pull", model],
                capture_output=True,
                text=True,
                timeout=600  # 10 minutos timeout
            )
            
            if result.returncode == 0:
                print(f"✅ {model} instalado com sucesso")
            else:
                print(f"❌ Erro ao instalar {model}: {result.stderr}")
                return False
        except subprocess.TimeoutExpired:
            print(f"❌ Timeout ao instalar {model}")
            return False
        except Exception as e:
            print(f"❌ Erro ao instalar {model}: {e}")
            return False
    
    return True

def check_python_dependencies():
    """Verifica se as dependências Python estão instaladas"""
    try:
        import streamlit
        import ollama
        import chromadb
        import sentence_transformers
        import PyPDF2
        import fitz
        import langchain
        return True
    except ImportError as e:
        print(f"❌ Dependência faltando: {e}")
        return False

def install_python_dependencies():
    """Instala dependências Python"""
    print("Instalando dependências Python...")
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Dependências Python instaladas")
            return True
        else:
            print(f"❌ Erro ao instalar dependências: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Erro ao instalar dependências: {e}")
        return False

def create_directories():
    """Cria diretórios necessários"""
    directories = [
        "data",
        "data/pdfs",
        "data/database",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Diretórios criados")

def run_streamlit():
    """Executa a aplicação Streamlit"""
    print("🚀 Iniciando aplicação Streamlit...")
    print("📱 Acesse: http://localhost:8501")
    print("⏹️  Pressione Ctrl+C para parar")
    
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Sistema encerrado pelo usuário")
    except Exception as e:
        print(f"❌ Erro ao executar Streamlit: {e}")

def main():
    """Função principal"""
    print("🧠 Sistema de Multiagentes para Análise de Neurocovid")
    print("=" * 50)
    
    # Verificar se o Ollama está rodando
    print("🔍 Verificando Ollama...")
    if not check_ollama_connection():
        print("❌ Ollama não está rodando!")
        print("💡 Inicie o Ollama com: ollama serve")
        print("📖 Ou instale: https://ollama.ai/")
        return
    
    print("✅ Ollama está rodando")
    
    # Verificar modelos do Ollama
    print("🔍 Verificando modelos do Ollama...")
    missing_models = check_ollama_models()
    
    if missing_models:
        print(f"⚠️  Modelos faltando: {', '.join(missing_models)}")
        response = input("Deseja instalar os modelos automaticamente? (s/n): ")
        
        if response.lower() in ['s', 'sim', 'y', 'yes']:
            if not install_ollama_models(missing_models):
                print("❌ Falha ao instalar modelos. Verifique sua conexão e tente novamente.")
                return
        else:
            print("❌ Modelos necessários não estão instalados. Sistema não pode continuar.")
            return
    else:
        print("✅ Todos os modelos necessários estão instalados")
    
    # Verificar dependências Python
    print("🔍 Verificando dependências Python...")
    if not check_python_dependencies():
        print("⚠️  Algumas dependências estão faltando")
        response = input("Deseja instalar as dependências automaticamente? (s/n): ")
        
        if response.lower() in ['s', 'sim', 'y', 'yes']:
            if not install_python_dependencies():
                print("❌ Falha ao instalar dependências Python")
                return
        else:
            print("❌ Dependências necessárias não estão instaladas")
            return
    else:
        print("✅ Todas as dependências Python estão instaladas")
    
    # Criar diretórios
    print("📁 Criando diretórios...")
    create_directories()
    
    # Executar aplicação
    print("\n🎯 Tudo pronto! Iniciando sistema...")
    time.sleep(2)
    run_streamlit()

if __name__ == "__main__":
    main()
