"""
Utilitários para processamento de PDFs
"""
import os
import fitz  # PyMuPDF
import PyPDF2
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging
from dataclasses import dataclass

@dataclass
class DocumentChunk:
    """Representa um chunk de documento processado"""
    content: str
    metadata: Dict[str, Any]
    page_number: int
    chunk_id: str

class PDFProcessor:
    """Classe para processamento de documentos PDF"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.logger = logging.getLogger(__name__)
    
    def extract_text_pymupdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Extrai texto usando PyMuPDF (mais robusto)"""
        try:
            doc = fitz.open(pdf_path)
            pages = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                
                # Extrair metadados da página
                metadata = {
                    "page_number": page_num + 1,
                    "file_path": pdf_path,
                    "file_name": Path(pdf_path).name
                }
                
                pages.append({
                    "text": text,
                    "metadata": metadata
                })
            
            doc.close()
            return pages
            
        except Exception as e:
            self.logger.error(f"Erro ao extrair texto com PyMuPDF: {e}")
            return []
    
    def extract_text_pypdf2(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Extrai texto usando PyPDF2 (fallback)"""
        try:
            pages = []
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    text = page.extract_text()
                    
                    metadata = {
                        "page_number": page_num + 1,
                        "file_path": pdf_path,
                        "file_name": Path(pdf_path).name
                    }
                    
                    pages.append({
                        "text": text,
                        "metadata": metadata
                    })
            
            return pages
            
        except Exception as e:
            self.logger.error(f"Erro ao extrair texto com PyPDF2: {e}")
            return []
    
    def extract_text(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Extrai texto do PDF usando o melhor método disponível"""
        # Tentar PyMuPDF primeiro
        pages = self.extract_text_pymupdf(pdf_path)
        
        # Se falhar, tentar PyPDF2
        if not pages:
            pages = self.extract_text_pypdf2(pdf_path)
        
        return pages
    
    def create_chunks(self, pages: List[Dict[str, Any]]) -> List[DocumentChunk]:
        """Cria chunks de texto para processamento"""
        chunks = []
        
        for page_data in pages:
            text = page_data["text"]
            metadata = page_data["metadata"]
            
            # Dividir texto em chunks
            words = text.split()
            current_chunk = []
            current_size = 0
            
            for word in words:
                current_chunk.append(word)
                current_size += len(word) + 1  # +1 para espaço
                
                if current_size >= self.chunk_size:
                    # Criar chunk
                    chunk_text = " ".join(current_chunk)
                    chunk_id = f"{metadata['file_name']}_page_{metadata['page_number']}_chunk_{len(chunks)}"
                    
                    chunk = DocumentChunk(
                        content=chunk_text,
                        metadata=metadata.copy(),
                        page_number=metadata["page_number"],
                        chunk_id=chunk_id
                    )
                    chunks.append(chunk)
                    
                    # Preparar próximo chunk com overlap
                    overlap_words = current_chunk[-self.chunk_overlap//10:] if self.chunk_overlap > 0 else []
                    current_chunk = overlap_words
                    current_size = sum(len(word) + 1 for word in overlap_words)
            
            # Adicionar último chunk se houver conteúdo
            if current_chunk:
                chunk_text = " ".join(current_chunk)
                chunk_id = f"{metadata['file_name']}_page_{metadata['page_number']}_chunk_{len(chunks)}"
                
                chunk = DocumentChunk(
                    content=chunk_text,
                    metadata=metadata.copy(),
                    page_number=metadata["page_number"],
                    chunk_id=chunk_id
                )
                chunks.append(chunk)
        
        return chunks
    
    def process_pdf(self, pdf_path: str) -> List[DocumentChunk]:
        """Processa um PDF completo e retorna chunks"""
        self.logger.info(f"Processando PDF: {pdf_path}")
        
        # Extrair texto
        pages = self.extract_text(pdf_path)
        if not pages:
            self.logger.error(f"Não foi possível extrair texto de: {pdf_path}")
            return []
        
        # Criar chunks
        chunks = self.create_chunks(pages)
        
        self.logger.info(f"PDF processado: {len(chunks)} chunks criados")
        return chunks
    
    def extract_metadata(self, pdf_path: str) -> Dict[str, Any]:
        """Extrai metadados do PDF"""
        try:
            doc = fitz.open(pdf_path)
            metadata = doc.metadata
            doc.close()
            
            # Adicionar informações do arquivo
            file_stats = os.stat(pdf_path)
            metadata.update({
                "file_size": file_stats.st_size,
                "file_path": pdf_path,
                "file_name": Path(pdf_path).name
            })
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"Erro ao extrair metadados: {e}")
            return {
                "file_path": pdf_path,
                "file_name": Path(pdf_path).name
            }

def validate_pdf(file_path: str) -> bool:
    """Valida se o arquivo é um PDF válido"""
    try:
        with open(file_path, 'rb') as file:
            header = file.read(4)
            return header == b'%PDF'
    except Exception:
        return False
