# Sistema de Multiagentes Multimodal para Análise de Neurocovid

## Descrição

Este sistema utiliza múltiplos agentes de IA especializados para análise de artigos científicos sobre manifestações neurológicas da COVID-19 (neurocovid). O sistema oferece uma interface web interativa construída com Streamlit e integração com modelos locais via Ollama.

## Características Principais

### 🤖 Sistema de Multiagentes
- **Agente de Busca**: Especializado em indexação e recuperação de documentos
- **Agente de Análise Médica**: Focado em análise de conteúdo médico e sintomas
- **Agente de Síntese**: Responsável por compilar e sintetizar informações
- **Agente Coordenador**: Orquestra as atividades entre os agentes

### 📚 Banco de Dados Vetorial
- Armazenamento e busca semântica de documentos PDF
- Indexação automática de artigos científicos
- Busca por similaridade usando embeddings

### 🖥️ Interface Multimodal
- Chat interativo com os agentes
- Upload e processamento de PDFs
- Busca avançada de documentos
- Geração de relatórios clínicos
- Análise comparativa de estudos

## Pré-requisitos

### Software Necessário
1. **Python 3.8+**
2. **Ollama** - Para modelos de IA locais
   ```bash
   # Instalar Ollama
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # Baixar modelos necessários
   ollama pull llama2:7b
   ollama pull nomic-embed-text
   ```

### Modelos Recomendados
- `llama2:7b` - Modelo principal para conversação
- `llama2:13b` - Versão mais robusta (opcional)
- `nomic-embed-text` - Para embeddings de texto

## Instalação

1. **Clone o repositório**
   ```bash
   git clone <repository-url>
   cd PROJETO_VITOR
   ```

2. **Instale as dependências**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verifique se o Ollama está rodando**
   ```bash
   ollama list
   ```

## Uso

### Iniciar o Sistema
```bash
streamlit run app.py
```

### Interface Web
Acesse `http://localhost:8501` no seu navegador.

### Funcionalidades Principais

#### 1. Upload de Documentos
- Vá para a sidebar "Gerenciamento de Documentos"
- Carregue arquivos PDF de artigos sobre neurocovid
- Clique em "Processar PDFs" para indexar no banco vetorial

#### 2. Chat Multimodal
- Use a aba "Chat Multimodal"
- Faça perguntas sobre neurocovid
- O sistema coordenará automaticamente os agentes apropriados

#### 3. Busca Avançada
- Use a aba "Busca Avançada"
- Busque por termos específicos nos documentos indexados
- Visualize resultados com scores de similaridade

#### 4. Análise Especializada
- Use a aba "Análise"
- Escolha o tipo de análise (sintomas, tratamentos, etc.)
- Obtenha análises médicas especializadas

#### 5. Relatórios
- Use a aba "Relatórios"
- Gere relatórios clínicos estruturados
- Crie resumos de pesquisa

## Estrutura do Projeto

```
PROJETO_VITOR/
├── app.py                 # Interface principal Streamlit
├── requirements.txt       # Dependências Python
├── README.md             # Este arquivo
├── config/
│   ├── __init__.py
│   └── settings.py       # Configurações do sistema
├── agents/
│   ├── __init__.py
│   ├── base_agent.py     # Classe base para agentes
│   ├── search_agent.py   # Agente de busca
│   ├── analysis_agent.py # Agente de análise médica
│   ├── synthesis_agent.py # Agente de síntese
│   └── coordinator_agent.py # Agente coordenador
├── database/
│   ├── __init__.py
│   └── vector_store.py   # Sistema de banco vetorial
├── utils/
│   ├── __init__.py
│   └── pdf_processor.py  # Processamento de PDFs
└── data/
    ├── pdfs/            # Diretório para PDFs
    └── database/        # Banco de dados vetorial
```

## Configuração Avançada

### Modelos Ollama
Edite `config/settings.py` para alterar modelos:
```python
OLLAMA_CONFIG = {
    "model": "llama2:13b",  # Modelo principal
    "embedding_model": "nomic-embed-text"
}
```

### Parâmetros de Chunking
Ajuste o processamento de PDFs:
```python
VECTOR_DB_CONFIG = {
    "chunk_size": 1000,     # Tamanho dos chunks
    "chunk_overlap": 200    # Sobreposição entre chunks
}
```

## Exemplos de Uso

### Perguntas Típicas
- "Quais são as principais manifestações neurológicas da COVID-19?"
- "Compare os tratamentos para anosmia pós-COVID"
- "Analise os sintomas neurológicos em pacientes graves"
- "Gere um resumo sobre encefalite relacionada à COVID-19"

### Tipos de Análise
- **Análise de Sintomas**: Identifica e analisa sintomas neurológicos
- **Análise de Tratamentos**: Avalia opções terapêuticas
- **Comparação de Estudos**: Compara diferentes pesquisas
- **Síntese de Literatura**: Compila informações de múltiplas fontes

## Solução de Problemas

### Ollama não conecta
```bash
# Verificar se Ollama está rodando
ollama list

# Reiniciar Ollama se necessário
ollama serve
```

### Erro de memória
- Use modelos menores (llama2:7b em vez de 13b)
- Reduza o tamanho dos chunks em `settings.py`

### PDFs não processam
- Verifique se os arquivos são PDFs válidos
- Certifique-se de que contêm texto (não apenas imagens)

## Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## Licença

Este projeto está sob licença MIT. Veja o arquivo LICENSE para detalhes.

## Suporte

Para suporte e dúvidas:
- Abra uma issue no GitHub
- Consulte a documentação do Ollama
- Verifique os logs do sistema em `logs/system.log`
