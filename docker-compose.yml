version: '3.8'

services:
  neurocovid-system:
    build: .
    container_name: neurocovid-multiagent
    ports:
      - "8501:8501"  # Streamlit
      - "11434:11434"  # Ollama
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ollama_data:/root/.ollama
    environment:
      - PYTHONPATH=/app
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_SERVER_PORT=8501
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  ollama_data:
    driver: local
